<script setup>
import { inject, reactive } from 'vue'
import router from '@/router/index.js'

const axios = inject('axios')

let loginData = reactive({
  businessName: '',
  password: '',
})

let login = () => {
  let url = '/api/business/login'
  axios
    .post(url, loginData)
    .then((response) => {
      console.log(response)
      if (response.data.status === 200) {
        router.push('/businessMain')
        sessionStorage.setItem('businessData', JSON.stringify(response.data.data))
      } else {
        alert(response.data.msg)
      }
    })
    .catch((error) => {
      console.log(error)
    })
}
</script>

<template>
  <h4>登录</h4>
  <input type="text" placeholder="请输入用户名" v-model="loginData.businessName" />
  <br />
  <input type="text" placeholder="请输入密码" v-model="loginData.password" />
  <br />
  <input type="button" value="登录" @click="login" />
</template>

<style scoped></style>
