<script setup>
import { reactive } from 'vue'
import axios from 'axios'
let businessId = JSON.parse(sessionStorage.getItem("businessData")).businessId;

let food = reactive({
  foodName: '',
  foodExplain: '',
  foodPrice: 0,
  businessId: businessId
});

let addFood = () => {
  let url = `http://127.0.0.1:8888/api/food/add`;
  axios.post(url, food).then((response) => {
    console.log(response);
    if (response.data.status === 200) {
      alert("添加成功");
    } else {
      alert(response.data.message);
    }
    food.foodName = '';
    food.foodExplain = '';
    food.foodPrice = 0;
  }).catch((error) => {
    console.log(error)
  })
}

</script>

<template>
<h4>添加食物</h4>
  <input type="text" placeholder="请输入食物名称" v-model="food.foodName">
  <br>
  <input type="text" placeholder="请输入食物介绍" v-model="food.foodExplain">
  <br>
  <input type="text" placeholder="请输入食物价格" v-model="food.foodPrice">
  <br>
  <input type="button" value="提交" @click="addFood">
</template>

<style scoped></style>
