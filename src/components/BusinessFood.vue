<script setup>
import axios from 'axios'
import { computed, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onUnmounted, onUpdated, ref } from 'vue'

let foodList = ref([])
let length = ref(0)
let price = ref(0.00)

let getAllFood = () => {
  let businessId = JSON.parse(sessionStorage.getItem('businessData')).businessId
  let url = `http://127.0.0.1:8888/api/food/${JSON.stringify(businessId)}`
  axios
    .get(url)
    .then((response) => {
      if (response.data.status === 200) {
        foodList.value = response.data.data
        length.value = computed(() => {
          return foodList.value.length;
        })
        price.value = computed(() => {
          let sum = 0;
          for (let i = 0; i < foodList.value.length; i++) {
            sum += foodList.value[i].foodPrice;
          }
          return sum;
        })
      } else {
        alert(response.data.message)
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

getAllFood()

onBeforeMount(() => {
  console.log('=========')
  console.log('DOM加载前')
})

onBeforeMount(() => {
  console.log('=========')
  console.log('DOM加载后')
})

onUpdated(() => {
  console.log('=========')
  console.log('DOM更新前')
})

onBeforeUpdate(() => {
  console.log('=========')
  console.log('DOM更新后')
})

onUnmounted(() => {
  console.log('=========')
  console.log('销毁前')
})

onBeforeUnmount(() => {
  console.log('==========')
  console.log('销毁后')
})
</script>

<template>
  <table>
    <thead>
      <tr>
        <th>食物Id</th>
        <th>食物名称</th>
        <th>食物介绍</th>
        <th>食物价格</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="food in foodList" :key="food.foodId">
        <td>{{ food.foodId }}</td>
        <td>{{ food.foodName }}</td>
        <td>{{ food.foodExplain }}</td>
        <td>{{ food.foodPrice }}</td>
      </tr>
    </tbody>
  </table>
  <p>菜品数量: {{length}}, 菜品总价: {{price}}</p>
</template>

<style scoped></style>
