import { createRouter, createWebHistory } from 'vue-router'
import BusinessLogin from '@/components/BusinessLogin.vue'
import BusinessMain from '@/components/BusinessMain.vue'
import BusinessFood from '@/components/BusinessFood.vue'
import BusinessAddFood from '@/components/BusinessAddFood.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [{
    path: '/',
    name: 'BusinessLogin',
    component: BusinessLogin
  }, {
    path: '/businessMain',
    name: 'BusinessMain',
    component: BusinessMain,
    children: [{
      path: '/businessMain/businessFood',
      name: 'BusinessFood',
      component: BusinessFood
    }, {
      path: '/businessMain/businessAddFood',
      name: 'BusinessAddFood',
      component: BusinessAddFood
    }, {
      path: '/businessMain',
      redirect: '/businessMain/businessFood'
    }]
  }],
})

export default router
